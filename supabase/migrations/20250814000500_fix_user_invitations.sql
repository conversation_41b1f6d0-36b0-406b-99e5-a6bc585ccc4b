-- Fix user_invitations table to add missing fields
-- This migration adds the missing invitation_token and status fields

-- Add missing columns to user_invitations table
ALTER TABLE public.user_invitations 
ADD COLUMN IF NOT EXISTS invitation_token TEXT NOT NULL DEFAULT gen_random_uuid()::text,
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
ADD COLUMN IF NOT EXISTS accepted_at TIMESTAMP WITH TIME ZONE;

-- Create unique index on invitation_token
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_invitations_token ON public.user_invitations(invitation_token);

-- Add index on status for performance
CREATE INDEX IF NOT EXISTS idx_user_invitations_status ON public.user_invitations(status);

-- Add index on email for lookups
CREATE INDEX IF NOT EXISTS idx_user_invitations_email ON public.user_invitations(email);

-- Add index on expires_at for cleanup
CREATE INDEX IF NOT EXISTS idx_user_invitations_expires_at ON public.user_invitations(expires_at);

-- Add RLS policy for user_invitations
ALTER TABLE public.user_invitations ENABLE ROW LEVEL SECURITY;

-- Only admins and project managers can view invitations
CREATE POLICY "Admins and project managers can view invitations" 
ON public.user_invitations FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.role IN ('admin', 'project_manager')
  )
);

-- Only admins and project managers can create invitations
CREATE POLICY "Admins and project managers can create invitations" 
ON public.user_invitations FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.role IN ('admin', 'project_manager')
  )
);

-- Only admins and project managers can update invitations
CREATE POLICY "Admins and project managers can update invitations" 
ON public.user_invitations FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.role IN ('admin', 'project_manager')
  )
);

-- Only admins can delete invitations
CREATE POLICY "Only admins can delete invitations" 
ON public.user_invitations FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM user_roles ur 
    WHERE ur.user_id = auth.uid() 
    AND ur.role = 'admin'
  )
);

-- Function to automatically expire old invitations
CREATE OR REPLACE FUNCTION expire_old_invitations()
RETURNS void AS $$
BEGIN
  UPDATE public.user_invitations 
  SET status = 'expired', updated_at = now()
  WHERE status = 'pending' 
  AND expires_at < now();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to run the expiration function (if pg_cron is available)
-- This would need to be set up manually in Supabase dashboard
-- SELECT cron.schedule('expire-invitations', '0 0 * * *', 'SELECT expire_old_invitations();');
