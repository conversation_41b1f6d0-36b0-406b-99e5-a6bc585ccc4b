import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface InvitationRequest {
  email: string;
  role: string;
  teamIds?: string[];
  personalMessage?: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Check required environment variables
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing required environment variables");
    }

    const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("No authorization header");
    }

    // Verify the user is admin or project manager
    const token = authHeader.replace("Bearer ", "");
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);

    if (authError || !user) {
      console.error("Auth error:", authError);
      throw new Error("Unauthorized");
    }

    // Check user permissions
    const { data: userRoles, error: rolesError } = await supabaseClient
      .from("user_roles")
      .select("role")
      .eq("user_id", user.id);

    if (rolesError) {
      console.error("Roles query error:", rolesError);
      // For now, allow if roles table doesn't exist or has issues
      console.log("Proceeding without role check due to database error");
    } else {
      const hasPermission = userRoles?.some(r => r.role === "admin" || r.role === "project_manager");
      if (!hasPermission && userRoles && userRoles.length > 0) {
        throw new Error("Insufficient permissions");
      }
      // If no roles found, allow for now (first user scenario)
      if (!userRoles || userRoles.length === 0) {
        console.log("No roles found for user, allowing invitation (first user scenario)");
      }
    }

    const { email, role, teamIds, personalMessage }: InvitationRequest = await req.json();

    if (!email || !role) {
      throw new Error("Email and role are required");
    }

    // Generate invitation token
    const invitationToken = crypto.randomUUID();

    // Create invitation record
    const { data: invitation, error: inviteError } = await supabaseClient
      .from("user_invitations")
      .insert({
        email,
        role,
        team_ids: teamIds || null,
        invited_by: user.id,
        invitation_token: invitationToken,
        status: 'pending',
        metadata: personalMessage ? { personal_message: personalMessage } : null
      })
      .select()
      .single();

    if (inviteError) {
      console.error("Invitation creation error:", inviteError);
      throw new Error("Failed to create invitation");
    }

    // Get inviter details
    const { data: inviterProfile } = await supabaseClient
      .from("profiles")
      .select("first_name, last_name")
      .eq("user_id", user.id)
      .single();

    const inviterName = inviterProfile 
      ? `${inviterProfile.first_name} ${inviterProfile.last_name}`.trim()
      : "RatioHub Team";

    // Send invitation email
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    if (!resendApiKey) {
      console.warn("RESEND_API_KEY not configured, skipping email send");
      // Return success even without email - invitation is still created
      return new Response(
        JSON.stringify({
          success: true,
          invitation: {
            id: invitation.id,
            email: invitation.email,
            status: invitation.status,
            expires_at: invitation.expires_at
          },
          message: "Invitation created but email not sent (email service not configured)"
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

    const resend = new Resend(resendApiKey);

    // Generate proper invite URL - use the actual app URL
    const appUrl = Deno.env.get("APP_URL") || "https://d3vvwsd9nr6exp.cloudfront.net";
    const inviteUrl = `${appUrl}/auth?invite=${invitation.invitation_token}`;
    
    const emailHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Join RatioHub</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: white; border-radius: 8px; padding: 40px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #4F46E5; margin: 0; font-size: 28px; font-weight: bold;">RatioHub</h1>
              </div>
              
              <h2 style="color: #1e293b; margin: 0 0 20px 0; font-size: 24px;">You're invited to join RatioHub!</h2>
              
              <p style="color: #64748b; line-height: 1.6; margin: 0 0 20px 0;">
                ${inviterName} has invited you to join their team on RatioHub as a <strong>${role}</strong>.
              </p>
              
              ${personalMessage ? `
                <div style="background: #f1f5f9; border-left: 4px solid #4F46E5; padding: 16px; margin: 20px 0; border-radius: 4px;">
                  <p style="color: #475569; margin: 0; font-style: italic;">"${personalMessage}"</p>
                </div>
              ` : ''}
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${inviteUrl}" style="display: inline-block; background: #4F46E5; color: white; text-decoration: none; padding: 14px 28px; border-radius: 6px; font-weight: 600; font-size: 16px;">
                  Accept Invitation
                </a>
              </div>
              
              <p style="color: #94a3b8; font-size: 14px; line-height: 1.5; margin: 20px 0 0 0; text-align: center;">
                This invitation will expire in 7 days. If you didn't expect this invitation, you can safely ignore this email.
              </p>
              
              <div style="border-top: 1px solid #e2e8f0; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #94a3b8; font-size: 12px; margin: 0;">
                  RatioHub - Project Management & Support System
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `;

    const { error: emailError } = await resend.emails.send({
      from: "RatioHub <<EMAIL>>",
      to: [email],
      subject: `You're invited to join RatioHub`,
      html: emailHtml,
    });

    if (emailError) {
      console.error("Email sending error:", emailError);
      // Don't fail the request if email fails, invitation is still created
    }

    console.log(`Invitation sent to ${email} by ${inviterName}`);

    return new Response(
      JSON.stringify({ 
        success: true, 
        invitation: {
          id: invitation.id,
          email: invitation.email,
          status: invitation.status,
          expires_at: invitation.expires_at
        }
      }),
      {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error("Error in send-invitation function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);