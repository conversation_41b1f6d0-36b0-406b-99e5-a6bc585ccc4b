import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TeamCreateSheet } from '@/components/teams/TeamCreateSheet';
import { TeamEditSheet } from '@/components/teams/TeamEditSheet';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowR<PERSON>, Calendar, Edit, MoreHorizontal, Plus, Trash2, User<PERSON>heck, Users } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Teams = () => {
  const navigate = useNavigate();
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [editingTeam, setEditingTeam] = useState<string | null>(null);
  const [deletingTeam, setDeletingTeam] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Delete team mutation
  const deleteTeam = useMutation({
    mutationFn: async (teamId: string) => {
      const { error } = await supabase
        .from('teams')
        .delete()
        .eq('id', teamId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Team deleted',
        description: 'Team has been deleted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      setDeletingTeam(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting team',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const { data: teams, isLoading, refetch } = useQuery({
    queryKey: ['teams'],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('teams')
          .select(`
            *,
            team_members(
              id,
              role,
              profiles(
                first_name,
                last_name,
                email
              )
            ),
            profiles:created_by(
              first_name,
              last_name
            )
          `)
          .eq('is_active', true)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Teams query failed:', error);
        return [];
      }
    },
  });

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-muted rounded-lg animate-pulse"></div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Teams</h1>
            <p className="text-muted-foreground">
              Manage your teams and collaborate with team members
            </p>
          </div>
          <Button onClick={() => setIsCreateSheetOpen(true)} className="gap-2">
            <Plus className="w-4 h-4" />
            New Team
          </Button>
        </div>

        {/* Teams Grid */}
        {teams && teams.length > 0 ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {teams.map((team: any) => (
              <Card key={team.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-lg">{team.name}</CardTitle>
                      <Badge variant="secondary" className="gap-1">
                        <Users className="w-3 h-3" />
                        {team.team_members?.length || 0}
                      </Badge>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditingTeam(team.id)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Team
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => setDeletingTeam(team)}
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete Team
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {team.description || 'No description provided'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Team Members */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium">
                      <UserCheck className="w-4 h-4" />
                      Team Members
                    </div>
                    <div className="flex -space-x-2">
                      {team.team_members?.slice(0, 5).map((member: any, index: number) => (
                        <Avatar key={member.id} className="w-8 h-8 border-2 border-background">
                          <AvatarFallback className="text-xs">
                            {member.profiles?.first_name?.[0]}{member.profiles?.last_name?.[0]}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      {team.team_members?.length > 5 && (
                        <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                          +{team.team_members.length - 5}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Team Info */}
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Created {team.created_at ? format(new Date(team.created_at), 'MMM d, yyyy') : 'Unknown'}
                    </div>
                    {team.profiles && (
                      <div>
                        Created by {team.profiles.first_name} {team.profiles.last_name}
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full gap-2"
                    onClick={() => navigate(`/teams/${team.id}`)}
                  >
                    <ArrowRight className="w-4 h-4" />
                    Manage Team
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No teams yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first team to start collaborating with others.
            </p>
            <Button onClick={() => setIsCreateSheetOpen(true)} className="gap-2">
              <Plus className="w-4 h-4" />
              Create Team
            </Button>
          </div>
        )}

        {/* Create Team Sheet */}
        <TeamCreateSheet
          open={isCreateSheetOpen}
          onOpenChange={setIsCreateSheetOpen}
          onTeamCreated={() => {
            refetch();
            setIsCreateSheetOpen(false);
          }}
        />

        {/* Edit Team Sheet */}
        {editingTeam && (
          <TeamEditSheet
            open={!!editingTeam}
            onOpenChange={(open) => !open && setEditingTeam(null)}
            onTeamUpdated={() => {
              refetch();
              setEditingTeam(null);
            }}
            teamId={editingTeam}
          />
        )}

        {/* Delete Confirmation Dialog */}
        <ConfirmDialog
          open={!!deletingTeam}
          onOpenChange={(open) => !open && setDeletingTeam(null)}
          onConfirm={() => deletingTeam && deleteTeam.mutate(deletingTeam.id)}
          title="Delete Team"
          description={`Are you sure you want to delete "${deletingTeam?.name}"? This action cannot be undone and will remove all team members and associated data.`}
          confirmText="Delete Team"
          variant="destructive"
          isLoading={deleteTeam.isPending}
        />
      </div>
    </DashboardLayout>
  );
};

export default Teams;