import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const teamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
  description: z.string().optional(),
});

type TeamFormData = z.infer<typeof teamSchema>;

interface TeamEditSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTeamUpdated: () => void;
  teamId: string;
}

export const TeamEditSheet: React.FC<TeamEditSheetProps> = ({
  open,
  onOpenChange,
  onTeamUpdated,
  teamId,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<TeamFormData>({
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Fetch team data
  const { data: team, isLoading: isLoadingTeam } = useQuery({
    queryKey: ['team', teamId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('teams')
        .select('*')
        .eq('id', teamId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!teamId && open,
  });

  // Update form when team data loads
  useEffect(() => {
    if (team) {
      form.reset({
        name: team.name,
        description: team.description || '',
      });
    }
  }, [team, form]);

  const updateTeamMutation = useMutation({
    mutationFn: async (data: TeamFormData) => {
      const teamData = {
        name: data.name,
        description: data.description || null,
        updated_at: new Date().toISOString(),
      };

      const { data: updatedTeam, error } = await supabase
        .from('teams')
        .update(teamData)
        .eq('id', teamId)
        .select()
        .single();

      if (error) throw error;
      return updatedTeam;
    },
    onSuccess: (updatedTeam: any) => {
      toast({
        title: 'Team updated successfully',
        description: `${updatedTeam?.name} has been updated.`,
      });
      queryClient.invalidateQueries({ queryKey: ['team', teamId] });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      onTeamUpdated();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating team',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: TeamFormData) => {
    updateTeamMutation.mutate(data);
  };

  if (isLoadingTeam) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="right" className="w-[400px] max-w-[90vw] overflow-y-auto">
          <div className="flex items-center justify-center h-full">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] max-w-[90vw] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Edit Team</SheetTitle>
          <SheetDescription>
            Update team details and information.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter team name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter team description" 
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={updateTeamMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateTeamMutation.isPending}
                  className="gap-2"
                >
                  {updateTeamMutation.isPending && (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  )}
                  Update Team
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
};
