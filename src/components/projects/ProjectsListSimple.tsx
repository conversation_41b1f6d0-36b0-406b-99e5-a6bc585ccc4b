import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
    Edit,
    ExternalLink,
    Folder,
    MoreHorizontal,
    Plus,
    Trash2
} from 'lucide-react';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ProjectEditSheet } from './ProjectEditSheet';

interface ProjectsListSimpleProps {
  projects: any[];
  isLoading: boolean;
  onRefresh: () => void;
  onCreateProject: () => void;
}

export const ProjectsListSimple: React.FC<ProjectsListSimpleProps> = ({
  projects,
  isLoading,
  onRefresh,
  onCreateProject
}) => {
  const navigate = useNavigate();
  const [editingProject, setEditingProject] = useState<string | null>(null);
  const [deletingProject, setDeletingProject] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: async (projectId: string) => {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Project deleted',
        description: 'Project has been deleted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      setDeletingProject(null);
      onRefresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting project',
        description: error.message,
        variant: 'destructive',
      });
    },
  });
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="space-y-2">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-2 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <Card className="p-12 text-center">
        <Folder className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No projects found</h3>
        <p className="text-muted-foreground mb-4">
          Get started by creating your first project.
        </p>
        <div className="flex gap-2 justify-center">
          <Button onClick={onCreateProject} className="gap-2">
            <Plus className="w-4 h-4" />
            Create Project
          </Button>
          <Button onClick={onRefresh} variant="outline">
            Refresh
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map((project: any) => (
          <Card key={project.id} className="group hover:shadow-md transition-shadow">
            <CardHeader className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1 flex-1">
                  <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors">
                    {project.name}
                  </h3>
                  <p className="text-sm text-muted-foreground font-mono">
                    {project.project_id}
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={
                    project.status === 'active' ? 'bg-green-500/10 text-green-600 border-green-200' :
                    project.status === 'completed' ? 'bg-emerald-500/10 text-emerald-600 border-emerald-200' :
                    project.status === 'planning' ? 'bg-blue-500/10 text-blue-600 border-blue-200' :
                    'bg-muted text-muted-foreground'
                  }>
                    {project.status?.replace('_', ' ').toUpperCase()}
                  </Badge>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => navigate(`/projects/${project.id}`)}>
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setEditingProject(project.id)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Project
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setDeletingProject(project)}
                        className="text-destructive"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete Project
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>

              {project.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {project.description}
                </p>
              )}
            </CardHeader>

            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Created: {new Date(project.created_at).toLocaleDateString()}
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => navigate(`/projects/${project.id}`)}
                >
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditingProject(project.id)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setDeletingProject(project)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Project Sheet */}
      {editingProject && (
        <ProjectEditSheet
          open={!!editingProject}
          onOpenChange={(open) => !open && setEditingProject(null)}
          onProjectUpdated={() => {
            onRefresh();
            setEditingProject(null);
          }}
          projectId={editingProject}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={!!deletingProject}
        onOpenChange={(open) => !open && setDeletingProject(null)}
        onConfirm={() => deletingProject && deleteProjectMutation.mutate(deletingProject.id)}
        title="Delete Project"
        description={`Are you sure you want to delete "${deletingProject?.name}"? This action cannot be undone and will remove all associated data including tasks, milestones, and time entries.`}
        confirmText="Delete Project"
        variant="destructive"
        isLoading={deleteProjectMutation.isPending}
      />
    </>
  );
};