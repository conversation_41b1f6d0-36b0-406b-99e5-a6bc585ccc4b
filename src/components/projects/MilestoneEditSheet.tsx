import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { z } from 'zod';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const milestoneSchema = z.object({
  name: z.string().min(1, 'Milestone name is required'),
  description: z.string().optional(),
  dueDate: z.date().optional(),
  status: z.enum(['pending', 'in_progress', 'completed', 'overdue']),
});

type MilestoneFormData = z.infer<typeof milestoneSchema>;

interface MilestoneEditSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMilestoneUpdated: () => void;
  milestoneId: string;
  projectId: string;
  phaseId?: string;
}

export const MilestoneEditSheet: React.FC<MilestoneEditSheetProps> = ({
  open,
  onOpenChange,
  onMilestoneUpdated,
  milestoneId,
  projectId,
  phaseId,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<MilestoneFormData>({
    resolver: zodResolver(milestoneSchema),
    defaultValues: {
      name: '',
      description: '',
      status: 'pending',
    },
  });

  // Fetch milestone data
  const { data: milestone, isLoading: isLoadingMilestone } = useQuery({
    queryKey: ['milestone', milestoneId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('milestones')
        .select('*')
        .eq('id', milestoneId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!milestoneId && open,
  });

  // Update form when milestone data loads
  useEffect(() => {
    if (milestone) {
      form.reset({
        name: milestone.name,
        description: milestone.description || '',
        dueDate: milestone.due_date ? new Date(milestone.due_date) : undefined,
        status: milestone.status || 'pending',
      });
    }
  }, [milestone, form]);

  const updateMilestoneMutation = useMutation({
    mutationFn: async (data: MilestoneFormData) => {
      const milestoneData = {
        name: data.name,
        description: data.description || null,
        due_date: data.dueDate ? format(data.dueDate, 'yyyy-MM-dd') : null,
        status: data.status,
        updated_at: new Date().toISOString(),
      };

      const { data: updatedMilestone, error } = await supabase
        .from('milestones')
        .update(milestoneData)
        .eq('id', milestoneId)
        .select()
        .single();

      if (error) throw error;
      return updatedMilestone;
    },
    onSuccess: (updatedMilestone: any) => {
      toast({
        title: 'Milestone updated successfully',
        description: `${updatedMilestone?.name} has been updated.`,
      });
      queryClient.invalidateQueries({ queryKey: ['project-milestones', projectId] });
      if (phaseId) {
        queryClient.invalidateQueries({ queryKey: ['phase-milestones', phaseId] });
      }
      queryClient.invalidateQueries({ queryKey: ['milestone', milestoneId] });
      onMilestoneUpdated();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating milestone',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: MilestoneFormData) => {
    updateMilestoneMutation.mutate(data);
  };

  if (isLoadingMilestone) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent side="right" className="w-[400px] max-w-[90vw] overflow-y-auto">
          <div className="flex items-center justify-center h-full">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] max-w-[90vw] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Edit Milestone</SheetTitle>
          <SheetDescription>
            Update milestone details and timeline.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Milestone Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter milestone name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter milestone description" 
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem key="pending" value="pending">Pending</SelectItem>
                        <SelectItem key="in_progress" value="in_progress">In Progress</SelectItem>
                        <SelectItem key="completed" value="completed">Completed</SelectItem>
                        <SelectItem key="overdue" value="overdue">Overdue</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={updateMilestoneMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateMilestoneMutation.isPending}
                  className="gap-2"
                >
                  {updateMilestoneMutation.isPending && (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  )}
                  Update Milestone
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
};
